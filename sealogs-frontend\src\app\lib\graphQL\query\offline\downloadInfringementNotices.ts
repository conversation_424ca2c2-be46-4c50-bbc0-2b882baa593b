import gql from 'graphql-tag'

export const DownloadInfringementNotices = gql`
    query DownloadInfringementNotices(
        $limit: Int = 100
        $offset: Int = 0
        $filter: InfringementNoticeFilterFields = {}
    ) {
        readInfringementNotices(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerAddress
                ownerPhone
                ownerEmail
                ownerDOB
                ownerOccupation
                infringementData
                otherDescription
                waterwaysOfficerID
                geoLocationID
                signatureID
                lat
                long
                signature {
                    id
                    signatureData
                }
            }
        }
    }
`
