import gql from 'graphql-tag'

export const DownloadDangerousGoodsRecords = gql`
    query DownloadDangerousGoodsRecords(
        $limit: Int = 100
        $offset: Int = 0
        $filter: DangerousGoodsRecordFilterFields = {}
    ) {
        readDangerousGoodsRecords(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                comment
                type
            }
        }
    }
`
