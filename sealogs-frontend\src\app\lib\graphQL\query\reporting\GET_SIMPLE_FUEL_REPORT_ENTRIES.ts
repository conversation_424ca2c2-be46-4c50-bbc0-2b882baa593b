import gql from 'graphql-tag'

export const GET_SIMPLE_FUEL_REPORT_ENTRIES = gql`
  query Simple_Fuel_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
      nodes{
        id
        startDate
        endDate
        lockedDate
        vehicleID
        archived
        signOffTimestamp
        state
        created
        vehicle{
          id
          clientID
          title
        }
        fuelLog(sort: {
          created: ASC
        }){
          nodes{
            id,
            fuelTankID
            fuelTank{
              id
              title
            }
            fuelTankID
            fuelBefore
            fuelAdded
            fuelAfter
            created
            date
          }
        }
        logBookEntrySections(
          sort: {
            created: ASC
          }
        ){
          nodes{
            id
            archived
            sortOrder
            logBookEntryID
            created
            sectionMemberComments(filter:{
              fieldName: {
                eq: "DailyCheckFuel"
              },
            }){
              nodes {
                  id
                  commentType
                  fieldName
                  comment
                  logBookEntrySectionID
                  hideComment
              }
            }
            tripEvents(sort: {
              created: ASC
            }){
              nodes{
                id
                eventCategory
                created
                eventType_RefuellingBunkering{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
                eventType_PassengerDropFacility{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
                eventType_Tasking{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`