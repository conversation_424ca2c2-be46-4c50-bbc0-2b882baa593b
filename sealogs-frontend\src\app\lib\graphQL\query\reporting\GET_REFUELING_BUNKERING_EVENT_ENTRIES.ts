import gql from "graphql-tag";

export const GET_REFUELING_BUNKERING_EVENT_ENTRIES = gql`
  query Refueling_Bunkering_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          sectionMemberComments(filter:{
              fieldName: {
                eq: "DailyCheckFuel"
              },
            }){
              nodes {
                  id
                  commentType
                  fieldName
                  comment
                  logBookEntrySectionID
                  hideComment
              }
          }
                    tripEvents(filter: {
            eventCategory: {
              eq: RefuellingBunkering
            }
          }){
            nodes{
              id
              eventCategory
              eventType_PassengerDropFacilityID
              location
              start
              end
             	eventType_RefuellingBunkering{
                id
                geoLocation{
                  id
                  title
                  lat
                  long
                }
                fuelLog{
                  nodes{
                    id
                    created
                    date
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    fuelTank{
                      id
                      title
                    }
                  }
                }
                title
                date
              }
            }
          }
        }
      }
    }
  }
}
`