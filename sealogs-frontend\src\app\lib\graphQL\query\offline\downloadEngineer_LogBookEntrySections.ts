import gql from 'graphql-tag'

export const DownloadEngineer_LogBookEntrySections = gql`
    query DownloadEngineer_LogBookEntrySections(
        $limit: Int = 100
        $offset: Int = 0
        $filter: Engineer_LogBookEntrySectionFilterFields = {}
    ) {
        readEngineer_LogBookEntrySections(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
            }
        }
    }
`
