import { DocumentNode, OperationVariables, QueryHookOptions } from '@apollo/client';
import { useLazyQuery } from '@apollo/client/react/hooks';

type Extractor<TResponse, TData> = (res: TResponse) => TData;

interface ConfiguredLazyQueryOpts<
  TResponse = any,
  TVariables extends OperationVariables = OperationVariables,
  TData = any,
> extends Omit<QueryHookOptions<TResponse, TVariables>, 'onCompleted' | 'onError'> {
  /** Pull just the piece of the response you care about. */
  extract: Extractor<TResponse, TData>;
  /** What to do with that piece. */
  onData: (data: TData) => void;
  /** Optional message prefix for console logging. */
  label?: string;
}

export function useConfiguredLazyQuery<
  TResponse = any,
  TVariables extends OperationVariables = OperationVariables,
  TData = any,
>({
  query,
  extract,
  onData,
  label,
  ...rest
}: ConfiguredLazyQueryOpts<TResponse, TVariables, TData> & { query: DocumentNode }) {
  const [exec, result] = useLazyQuery<TResponse, TVariables>(query, {
    fetchPolicy: 'cache-and-network',
    onCompleted: (response) => {
      try {
        onData(extract(response));
      } catch (err) {
        console.error(`${label ?? 'Query'} extract/onData error`, err);
      }
    },
    onError: (err) => console.error(`${label ?? 'Query'} error`, err),
    ...rest, // let callers override defaults if they really need to
  });

  return [exec, result] as const;
}
