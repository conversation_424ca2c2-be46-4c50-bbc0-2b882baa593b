import gql from 'graphql-tag'

export const DownloadEventType_Supernumeraries = gql`
    query DownloadEventType_Supernumeraries(
        $limit: Int = 100
        $offset: Int = 0
        $filter: EventType_SupernumeraryFilterFields = {}
    ) {
        readEventType_Supernumeraries(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                title
                totalGuest
                focGuest
                isBriefed
                briefingTime
                guestList {
                    nodes {
                        id
                    }
                }
            }
        }
    }
`
