import gql from 'graphql-tag'

export const DownloadDangerousGoods = gql`
    query DownloadDangerousGoods(
        $limit: Int = 100
        $offset: Int = 0
        $filter: DangerousGoodFilterFields = {}
    ) {
        readDangerousGoods(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                archived
                title
            }
        }
    }
`
