import gql from 'graphql-tag'

// GET_GEO_LOCATIONS
export const ReadGeoLocations = gql`
    query ReadGeoLocations($limit: Int = 100, $offset: Int = 0) {
        readGeoLocations(limit: $limit, offset: $offset) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                title
                lat
                long
            }
        }
    }
`

// GetFavoriteLocations
export const ReadFavoriteLocations = gql`
    query ReadFavoriteLocations($userID: ID!) {
        readFavoriteLocations(
            filter: { memberID: { eq: $userID } }
            sort: { usage: DESC }
        ) {
            nodes {
                id
                usage
                geoLocationID
            }
        }
    }
`
