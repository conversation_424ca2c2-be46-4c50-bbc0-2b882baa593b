'use client'

/**
 * CrewMemberAvatars - A reusable component for displaying crew member avatars with tooltips and overflow handling
 *
 * @example
 * ```tsx
 * <CrewMemberAvatars
 *   members={[
 *     { id: 1, firstName: '<PERSON>', surname: '<PERSON><PERSON>' },
 *     { id: 2, firstName: '<PERSON>', surname: '<PERSON>' }
 *   ]}
 *   maxVisible={3}
 *   avatarSize="sm"
 *   getAvatarVariant={(member) => member.isOverdue ? 'destructive' : 'secondary'}
 * />
 * ```
 */

import React from 'react'
import {
    Avatar,
    AvatarFallback,
    getCrewInitials,
    Button,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { cn } from '@/app/lib/utils'

export interface CrewMember {
    id: string | number
    firstName?: string
    surname?: string
}

export interface CrewMemberAvatarsProps {
    /** Array of crew members to display */
    members: CrewMember[]
    /** Maximum number of avatars to show before overflow (default: 3) */
    maxVisible?: number
    /** Size of the avatars (default: 'sm') */
    avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    /** Size of the avatars in the popover (default: 'xs') */
    popoverAvatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    /** Function to determine avatar variant based on member and index */
    getAvatarVariant?: (
        member: CrewMember,
        index: number,
    ) => 'default' | 'success' | 'secondary' | 'destructive' | 'warning'
    /** Whether tooltips and popovers should be clickable on mobile (default: true) */
    mobileClickable?: boolean
    /** Additional CSS classes for the container */
    className?: string
    /** Custom button text for overflow (default: "+{count} more") */
    overflowButtonText?: (count: number) => string
}

export const CrewMemberAvatars: React.FC<CrewMemberAvatarsProps> = ({
    members = [],
    maxVisible = 3,
    avatarSize = 'sm',
    popoverAvatarSize = 'xs',
    getAvatarVariant = () => 'secondary',
    mobileClickable = true,
    className,
    overflowButtonText = (count) => `+${count} more`,
}) => {
    // Filter out invalid members and ensure we have valid data
    const validMembers = members.filter(
        (member) => member && (member.firstName || member.surname),
    )

    if (validMembers.length === 0) {
        return null
    }

    const visibleMembers = validMembers.slice(0, maxVisible)
    const overflowMembers = validMembers.slice(maxVisible)
    const hasOverflow = overflowMembers.length > 0

    return (
        <div className={cn('flex gap-1 items-center', className)}>
            {/* Visible avatars with tooltips */}
            {visibleMembers.map((member, index) => (
                <Tooltip
                    key={member.id || index}
                    mobileClickable={mobileClickable}>
                    <TooltipTrigger mobileClickable={mobileClickable}>
                        <Avatar
                            size={avatarSize}
                            variant={getAvatarVariant(member, index)}>
                            <AvatarFallback
                                className={
                                    avatarSize === 'xs' ? 'text-xs' : 'text-sm'
                                }>
                                {getCrewInitials(
                                    member.firstName,
                                    member.surname,
                                )}
                            </AvatarFallback>
                        </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                        {member.firstName} {member.surname ?? ''}
                    </TooltipContent>
                </Tooltip>
            ))}

            {/* Overflow button with popover */}
            {hasOverflow && (
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-xs">
                            {overflowButtonText(overflowMembers.length)}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-64">
                        <div className="p-3 max-h-64 overflow-auto">
                            <div className="space-y-2">
                                {overflowMembers.map((member) => (
                                    <div
                                        key={member.id}
                                        className="text-sm flex items-center gap-2">
                                        <Avatar
                                            size={popoverAvatarSize}
                                            variant="secondary">
                                            <AvatarFallback className="text-xs">
                                                {getCrewInitials(
                                                    member.firstName,
                                                    member.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                        {`${member.firstName ?? ''} ${member.surname ?? ''}`}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            )}
        </div>
    )
}

export default CrewMemberAvatars
