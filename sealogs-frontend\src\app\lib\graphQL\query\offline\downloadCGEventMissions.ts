import gql from 'graphql-tag'

export const DownloadCGEventMissions = gql`
    query DownloadCGEventMissions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: CGEventMissionFilterFields = {}
    ) {
        readCGEventMissions(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                missionType
                description
                operationOutcome
                completedAt
                eventID
                eventType
                operationDescription
                lat
                long
                currentLocationID
                vesselRescueID
                personRescueID
                vesselPositionID
                vesselID
            }
        }
    }
`
