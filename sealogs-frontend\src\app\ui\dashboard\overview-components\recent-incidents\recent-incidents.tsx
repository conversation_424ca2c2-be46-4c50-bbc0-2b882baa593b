'use client'
import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useLazyQuery } from '@apollo/client'
import { createColumns, DataTable } from '@/components/filteredTable'
import { HorizontalBarChartComponent } from '@/components/horizontal-bar-chart'
import { PieChartComponent } from '@/components/pie-chart'
import { ChartConfig } from '@/components/ui/chart'
import { H1, P } from '@/components/ui/typography'
import { formatDate } from '@/app/helpers/dateHelper'
import { SealogsIncidentIcon } from '@/app/lib/icons/SealogsIncidentIcon'
import { GET_INCIDENT_RECORDS } from '@/app/ui/incident-record/graphql/queries'

interface IncidentData {
    id: string
    title: string
    startDate: string
    incidentType: string
    vessel: {
        id: string
        title: string
    }
    reportedBy: {
        id: string
        firstName: string
        surname: string
    }
}

interface VesselIncidentCount {
    vesselId: string
    vesselName: string
    count: number
}

interface IncidentTypeCount {
    type: string
    count: number
}

export default function RecentIncidents() {
    const [recentIncidents, setRecentIncidents] = useState<IncidentData[]>([])
    const [vesselIncidentCounts, setVesselIncidentCounts] = useState<
        VesselIncidentCount[]
    >([])
    const [incidentTypeCounts, setIncidentTypeCounts] = useState<
        IncidentTypeCount[]
    >([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const [queryDashboardIncidentData] = useLazyQuery(GET_INCIDENT_RECORDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readIncidentRecords.nodes
            if (data) {
                processIncidentData(data)
            }
        },
        onError: (error: any) => {
            console.error('queryDashboardIncidentData error', error)
            setError('Failed to load incident data')
        },
    })

    const [queryRecentIncidents] = useLazyQuery(GET_INCIDENT_RECORDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readIncidentRecords.nodes
            if (data) {
                setRecentIncidents(data.slice(0, 5))
            }
        },
        onError: (error: any) => {
            console.error('queryRecentIncidents error', error)
            setError('Failed to load recent incidents')
        },
    })

    useEffect(() => {
        loadIncidentData()
        setIsLoading(false)
    }, []) // Empty dependency array to run only on mount

    const loadIncidentData = async () => {
        const twelveMonthsAgo = new Date()
        twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)

        await queryDashboardIncidentData({
            variables: {
                filter: {
                    startDate: {
                        gte: twelveMonthsAgo.toISOString(),
                    },
                },
                limit: 1000,
            },
        })

        await queryRecentIncidents({
            variables: {
                limit: 5,
            },
        })
    }

    const processIncidentData = (incidents: any[]) => {
        // Process vessel incident counts
        const vesselCounts: { [key: string]: VesselIncidentCount } = {}
        const typeCounts: { [key: string]: number } = {}

        incidents.forEach((incident) => {
            // Count by vessel - only include vessels with valid data
            if (
                incident.vessel &&
                incident.vessel.title &&
                incident.vessel.id !== '0'
            ) {
                const vesselId = incident.vessel.id
                const vesselName = incident.vessel.title

                if (!vesselCounts[vesselId]) {
                    vesselCounts[vesselId] = {
                        vesselId,
                        vesselName: vesselName,
                        count: 0,
                    }
                }
                vesselCounts[vesselId].count++
            }

            // Count by incident type
            if (incident.incidentType) {
                typeCounts[incident.incidentType] =
                    (typeCounts[incident.incidentType] || 0) + 1
            }
        })

        // Filter out vessels with zero incidents and sort by count
        const vesselData = Object.values(vesselCounts)
            .filter((vessel) => vessel.count > 0)
            .sort((a, b) => b.count - a.count)

        const typeData = Object.entries(typeCounts)
            .map(([type, count]) => ({ type, count }))
            .sort((a, b) => b.count - a.count)

        setVesselIncidentCounts(vesselData)
        setIncidentTypeCounts(typeData)
    }

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return (
                    <div className="flex flex-col">
                        <Link
                            href={`/incident-records/edit/?id=${incident.id}`}
                            className="font-medium hover:text-curious-blue-400">
                            {incident.title || 'Untitled Incident'}
                        </Link>
                        <div className="text-curious-blue-400 uppercase text-[10px]">
                            {incident.incidentType}
                        </div>
                        {incident.vessel && (
                            <div className="text-xs text-muted-foreground">
                                {incident.vessel?.title}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'startDate',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return (
                    <div className="text-right text-sm">
                        {formatDate(incident.startDate)}
                    </div>
                )
            },
        },
    ])

    // Chart data for vessels
    const vesselChartData = vesselIncidentCounts.map((vessel) => ({
        title: vessel.vesselName,
        amount: vessel.count,
        fill: 'var(--color-incidents)',
        stroke: 'hsl(207, 86%, 39%)',
    }))

    // Create dynamic vessel chart config with each vessel name as a key
    const vesselChartConfig = {
        amount: {
            label: 'Incidents',
        },
        incidents: {
            label: 'Incidents',
            color: 'var(--chart-3)', // Blue color scheme
        },
        // Add each vessel name as a config key
        ...vesselIncidentCounts.reduce(
            (config, vessel) => {
                config[vessel.vesselName] = {
                    label: vessel.vesselName,
                }
                return config
            },
            {} as Record<string, { label: string }>,
        ),
    } satisfies ChartConfig

    // Manual color mapping for incident types with appropriate borders
    const getIncidentTypeColor = (incidentType: string) => {
        const colorMap: Record<string, string> = {
            incident: 'var(--chart-1)',
            accident: 'var(--chart-3)',
            nearMiss: 'var(--chart-4)',
            mental: 'var(--chart-5)',
        }
        return colorMap[incidentType] || 'var(--chart-2)' // Default color
    }

    const getIncidentTypeBorder = (incidentType: string) => {
        const borderMap: Record<string, string> = {
            incident: 'hsl(1, 80%, 35%)', // curious-blue-800
            accident: 'hsl(207, 33%, 27%)', // cinnabar-800
            nearMiss: 'hsl(207, 35%, 39%)', // fire-bush-800
            mental: 'hsl(177, 87%, 22%)', // wedgewood-800
        }
        return borderMap[incidentType] || 'hsl(15, 71%, 31%)' // Default fire-bush-800
    }

    // Chart data for incident types with manual colors and borders
    const typeChartData = incidentTypeCounts.map((type) => ({
        title: type.type,
        amount: type.count,
        fill: getIncidentTypeColor(type.type),
        stroke: getIncidentTypeBorder(type.type),
    }))

    // Manual chart config with hardcoded colors for incident types
    const typeChartConfig = {
        amount: {
            label: 'Incidents',
        },
        incidents: {
            label: 'Incidents',
        },
        incident: {
            label: 'Incident',
            color: 'var(--chart-1)',
        },
        accident: {
            label: 'Accident',
            color: 'var(--chart-3)',
        },
        nearMiss: {
            label: 'Near Miss',
            color: 'var(--chart-4)',
        },
        mental: {
            label: 'Mental',
            color: 'var(--chart-5)',
        },
        // Default fallback
        default: {
            label: 'Other',
            color: 'var(--chart-2)',
        },
    } satisfies ChartConfig

    if (error) {
        return (
            <div className="space-y-2">
                <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                    <SealogsIncidentIcon className="size-9" />
                    <Link href="/incident-records">
                        <H1>Recent Incidents</H1>
                    </Link>
                </div>
                <div className="flex justify-center items-center p-8">
                    <p className="text-red-500">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-2">
            <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                <SealogsIncidentIcon className="size-9" />
                <Link href="/incident-records">
                    <H1>Recent Incidents</H1>
                </Link>
            </div>

            {/* Charts Section */}
            <div className="grid xs:grid-cols-2 xs:h-80 gap-4">
                <div>
                    {isLoading ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-curious-blue-400"></div>
                        </div>
                    ) : vesselChartData.length > 0 ? (
                        <HorizontalBarChartComponent
                            chartConfig={vesselChartConfig}
                            chartData={vesselChartData}
                            cardTitle="Incidents by Vessel(12 months)"
                            cardInfo=""
                        />
                    ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                            <div className="text-center">
                                <p className="text-sm">
                                    No vessel data available
                                </p>
                                <p className="text-xs">
                                    Incidents may not have valid vessel
                                    assignments
                                </p>
                            </div>
                        </div>
                    )}
                </div>
                <div className="flex flex-col">
                    {isLoading ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-curious-blue-400"></div>
                        </div>
                    ) : (
                        <>
                            <PieChartComponent
                                chartConfig={typeChartConfig}
                                chartData={typeChartData}
                            />
                            <p>Incidents by Type</p>
                        </>
                    )}
                </div>
            </div>

            {/* Recent Incidents List */}
            <div className="space-y-5">
                {recentIncidents && recentIncidents.length > 0 ? (
                    <DataTable
                        columns={columns}
                        data={recentIncidents}
                        showToolbar={false}
                        className="p-0 pt-3 border-0 shadow-none"
                    />
                ) : (
                    <div className="flex justify-between items-center gap-2 p-2 pt-4">
                        <div>
                            <svg
                                className="!w-[75px] h-auto"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 148.02 147.99">
                                <path
                                    d="M70.84.56c16-.53,30.66,3.59,43.98,12.35,12.12,8.24,21.1,19.09,26.92,32.55,6.14,14.85,7.38,30.11,3.74,45.78-3.92,15.59-11.95,28.57-24.1,38.96-13.11,10.9-28.24,16.66-45.39,17.28-16.75.33-31.88-4.39-45.39-14.17-13.29-9.92-22.34-22.84-27.16-38.76-4.03-14.16-3.9-28.29.39-42.38,5-15.45,14-27.97,27.01-37.6C42.77,5.97,56.1,1.31,70.84.56Z"
                                    fill="#fefefe"
                                    fillRule="evenodd"
                                    stroke="#024450"
                                    strokeMiterlimit="10"
                                    strokeWidth="1.02px"
                                />
                            </svg>
                        </div>
                        <p>
                            Great news! No recent incidents to report. Your
                            safety record is looking excellent!
                        </p>
                    </div>
                )}
                <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                    <Link
                        href="/incident-records"
                        className="text-accent-foreground uppercase group hover:text-curious-blue-400 text-xs">
                        View All{' '}
                        <span className="hidden group-hover:text-curious-blue-400 md:inline-block">
                            &nbsp;Incidents&nbsp;
                        </span>
                    </Link>
                </div>
            </div>
        </div>
    )
}
