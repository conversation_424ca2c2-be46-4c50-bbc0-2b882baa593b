import gql from 'graphql-tag'

export const DownloadConsequences = gql`
    query DownloadConsequences(
        $limit: Int = 100
        $offset: Int = 0
        $filter: ConsequenceFilterFields = {}
    ) {
        readConsequences(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                name
                humanInjury
                financialCost
                workIncomeReputation
                environment
                backgroundColour
                textColour
                number
            }
        }
    }
`
