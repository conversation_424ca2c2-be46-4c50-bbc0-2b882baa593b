import gql from 'graphql-tag'

export const DownloadGeoLocations = gql`
    query DownloadGeoLocations(
        $limit: Int = 100
        $offset: Int = 0
        $filter: GeoLocationFilterFields = {}
    ) {
        readGeoLocations(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                archived
                title
                lat
                long
            }
        }
    }
`
