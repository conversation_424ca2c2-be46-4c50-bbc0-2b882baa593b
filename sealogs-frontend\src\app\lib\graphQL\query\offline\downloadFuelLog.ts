import gql from 'graphql-tag'

export const DownloadFuelLog = gql`
    query DownloadFuelLog(
        $limit: Int = 100
        $offset: Int = 0
        $filter: FuelLogFilterFields = {}
    ) {
        readFuelLogs(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                fuelAdded
                fuelBefore
                fuelAfter
                date
                created
                costPerLitre
                totalCost
                refuellingBunkeringID
                tripUpdateID
                eventType_TaskingID
                eventType_PassengerDropFacilityID
                logBookEntryID
                fuelTankID
                notes
            }
        }
    }
`
