'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { SealogsCogIcon } from '@/app/lib/icons'
import { DownloadIcon, FileDown, FileText } from 'lucide-react'
import { Button } from '@/components/ui'
import { useRouter } from 'next/navigation'

interface MaintenanceReportFilterActionsProps {
    onDownloadCsv?: () => void
    onDownloadPdf?: () => void
}

export const MaintenanceReportFilterActions = ({
    onDownloadCsv,
    onDownloadPdf,
}: MaintenanceReportFilterActionsProps) => {
    const { isMobile } = useSidebar()
    const router = useRouter()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <div className="text-input flex flex-col items-center justify-center py-[9px]">
                    <DropdownMenuItem
                        variant="backButton"
                        onClick={() => router.push('/reporting')}>
                        Back
                    </DropdownMenuItem>
                    {onDownloadPdf && (
                        <DropdownMenuItem className='px-[26px]' onClick={onDownloadPdf}>
                            Download PDF
                        </DropdownMenuItem>
                    )}
                    {onDownloadCsv && (
                        <DropdownMenuItem className='px-[26px]' onClick={onDownloadCsv}>
                            Download CSV
                        </DropdownMenuItem>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
