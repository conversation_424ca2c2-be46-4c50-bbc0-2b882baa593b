'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { HorizontalBarChartComponent } from '@/components/horizontal-bar-chart'
import { PieChartComponent } from '@/components/pie-chart'
import { ChartConfig } from '@/components/ui/chart'
import { P } from '@/components/ui/typography'
import { GET_INCIDENT_RECORDS } from '../graphql/queries'
import { Card } from '@/components/ui'

interface IncidentData {
    id: string
    title: string
    startDate: string
    incidentType: string
    contributingFactor: string
    treatment: string
    vessel: {
        id: string
        title: string
    }
}

interface VesselIncidentCount {
    vesselId: string
    vesselName: string
    count: number
}

interface IncidentTypeCount {
    type: string
    count: number
}

interface ContributingFactorCount {
    factor: string
    count: number
}

interface TreatmentCount {
    treatment: string
    count: number
}

export default function IncidentChartsDashboard() {
    const [vesselIncidentCounts, setVesselIncidentCounts] = useState<
        VesselIncidentCount[]
    >([])
    const [incidentTypeCounts, setIncidentTypeCounts] = useState<
        IncidentTypeCount[]
    >([])
    const [contributingFactorCounts, setContributingFactorCounts] = useState<
        ContributingFactorCount[]
    >([])
    const [treatmentCounts, setTreatmentCounts] = useState<TreatmentCount[]>([])
    const [isLoading, setIsLoading] = useState(true)

    const [queryIncidentData] = useLazyQuery(GET_INCIDENT_RECORDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readIncidentRecords.nodes
            if (data) {
                processIncidentData(data)
            }
            setIsLoading(false)
        },
        onError: (error: any) => {
            console.error('queryIncidentData error', error)
            setIsLoading(false)
        },
    })

    useEffect(() => {
        loadIncidentData()
    }, [])

    const loadIncidentData = async () => {
        await queryIncidentData({
            variables: {
                limit: 1000, // Get all incidents for comprehensive charts
            },
        })
    }

    const processIncidentData = (incidents: IncidentData[]) => {
        // Process vessel incident counts
        const vesselCounts: { [key: string]: VesselIncidentCount } = {}
        const typeCounts: { [key: string]: number } = {}
        const factorCounts: { [key: string]: number } = {}
        const treatmentCountsMap: { [key: string]: number } = {}

        incidents.forEach((incident) => {
            // Count by vessel - only include vessels with valid data
            if (
                incident.vessel &&
                incident.vessel.title &&
                incident.vessel.id !== '0'
            ) {
                const vesselId = incident.vessel.id
                const vesselName = incident.vessel.title

                if (!vesselCounts[vesselId]) {
                    vesselCounts[vesselId] = {
                        vesselId,
                        vesselName,
                        count: 0,
                    }
                }
                vesselCounts[vesselId].count++
            }

            // Count by incident type
            if (incident.incidentType) {
                typeCounts[incident.incidentType] =
                    (typeCounts[incident.incidentType] || 0) + 1
            }

            // Count by contributing factor
            if (incident.contributingFactor) {
                factorCounts[incident.contributingFactor] =
                    (factorCounts[incident.contributingFactor] || 0) + 1
            }

            // Count by treatment
            if (incident.treatment) {
                treatmentCountsMap[incident.treatment] =
                    (treatmentCountsMap[incident.treatment] || 0) + 1
            }
        })

        // Convert to arrays and sort
        setVesselIncidentCounts(
            Object.values(vesselCounts).sort((a, b) => b.count - a.count),
        )

        setIncidentTypeCounts(
            Object.entries(typeCounts)
                .map(([type, count]) => ({ type, count }))
                .sort((a, b) => b.count - a.count),
        )

        setContributingFactorCounts(
            Object.entries(factorCounts)
                .map(([factor, count]) => ({ factor, count }))
                .sort((a, b) => b.count - a.count),
        )

        setTreatmentCounts(
            Object.entries(treatmentCountsMap)
                .map(([treatment, count]) => ({ treatment, count }))
                .sort((a, b) => b.count - a.count),
        )
    }

    // Calculate totals and statistics for enhanced bar chart
    const totalIncidents = vesselIncidentCounts.reduce(
        (sum, vessel) => sum + vessel.count,
        0,
    )
    const averageIncidentsPerVessel =
        totalIncidents > 0
            ? (totalIncidents / vesselIncidentCounts.length).toFixed(1)
            : '0'
    const topVessel =
        vesselIncidentCounts.length > 0 ? vesselIncidentCounts[0] : null

    // Chart configurations and data with enhanced information
    const vesselChartData = vesselIncidentCounts.map((vessel) => {
        const percentage =
            totalIncidents > 0
                ? ((vessel.count / totalIncidents) * 100).toFixed(1)
                : '0'
        return {
            title: vessel.vesselName,
            amount: vessel.count,
            percentage: `${percentage}%`,
            fill: 'var(--chart-3)',
            stroke: 'hsl(207, 86%, 39%)',
        }
    })

    const vesselChartConfig = {
        amount: {
            label: 'Incidents',
        },
        ...vesselIncidentCounts.reduce(
            (config, vessel) => ({
                ...config,
                [vessel.vesselName]: {
                    label: vessel.vesselName,
                    color: 'var(--chart-3)',
                },
            }),
            {} as Record<string, { label: string; color: string }>,
        ),
    } satisfies ChartConfig

    // Enhanced card info for vessel chart - simplified design
    const vesselChartInfo = (
        <>
            <span className="text-curious-blue-400 text-sm">
                Top: {topVessel?.vesselName} ({topVessel?.count})
            </span>
        </>
    )

    // Incident type chart configuration with manual colors
    const getIncidentTypeColor = (incidentType: string) => {
        const colorMap: Record<string, string> = {
            incident: 'var(--chart-1)',
            accident: 'var(--chart-3)',
            nearMiss: 'var(--chart-4)',
            mental: 'var(--chart-5)',
        }
        return colorMap[incidentType] || 'var(--chart-2)'
    }

    const getIncidentTypeBorder = (incidentType: string) => {
        const borderMap: Record<string, string> = {
            incident: 'hsl(1, 80%, 35%)',
            accident: 'hsl(207, 33%, 27%)',
            nearMiss: 'hsl(207, 35%, 39%)',
            mental: 'hsl(177, 87%, 22%)',
        }
        return borderMap[incidentType] || 'hsl(15, 71%, 31%)'
    }

    // Helper function to get border color for chart variables
    const getChartColorBorder = (chartIndex: number) => {
        const borderMap: Record<number, string> = {
            1: 'hsl(1, 80%, 35%)', // cinnabar-800 for chart-1
            2: 'hsl(15, 71%, 31%)', // fire-bush-800 for chart-2
            3: 'hsl(208, 79%, 27%)', // curious-blue-800 for chart-3
            4: 'hsl(207, 33%, 27%)', // wedgewood-800 for chart-4
            5: 'hsl(177, 87%, 22%)', // bright-turquoise-800 for chart-5
        }
        return borderMap[chartIndex] || 'hsl(15, 71%, 31%)' // Default to fire-bush-800
    }

    const typeChartData = incidentTypeCounts.map((type) => ({
        title: type.type,
        amount: type.count,
        fill: getIncidentTypeColor(type.type),
        stroke: getIncidentTypeBorder(type.type),
    }))

    const typeChartConfig = {
        amount: {
            label: 'Incidents',
        },
        incidents: {
            label: 'Incidents',
        },
        incident: {
            label: 'Incident',
            color: 'var(--chart-1)',
        },
        accident: {
            label: 'Accident',
            color: 'var(--chart-3)',
        },
        nearMiss: {
            label: 'Near Miss',
            color: 'var(--chart-4)',
        },
        mental: {
            label: 'Mental',
            color: 'var(--chart-5)',
        },
        default: {
            label: 'Other',
            color: 'var(--chart-2)',
        },
    } satisfies ChartConfig

    // Contributing factor chart configuration
    const factorChartData = contributingFactorCounts.map((factor, index) => {
        const chartIndex = (index % 5) + 1
        return {
            title: factor.factor,
            amount: factor.count,
            fill: `var(--chart-${chartIndex})`,
            stroke: getChartColorBorder(chartIndex),
        }
    })

    const factorChartConfig = {
        amount: {
            label: 'Incidents',
        },
        ...contributingFactorCounts.reduce(
            (config, factor, index) => ({
                ...config,
                [factor.factor]: {
                    label:
                        factor.factor.charAt(0).toUpperCase() +
                        factor.factor.slice(1),
                    color: `var(--chart-${(index % 5) + 1})`,
                },
            }),
            {} as Record<string, { label: string; color: string }>,
        ),
    } satisfies ChartConfig

    // Treatment chart configuration
    const treatmentChartData = treatmentCounts.map((treatment, index) => {
        const chartIndex = ((index + 2) % 5) + 1 // Offset colors to differentiate from factors
        return {
            title: treatment.treatment,
            amount: treatment.count,
            fill: `var(--chart-${chartIndex})`,
            stroke: getChartColorBorder(chartIndex),
        }
    })

    const treatmentChartConfig = {
        amount: {
            label: 'Incidents',
        },
        ...treatmentCounts.reduce(
            (config, treatment, index) => ({
                ...config,
                [treatment.treatment]: {
                    label:
                        treatment.treatment.charAt(0).toUpperCase() +
                        treatment.treatment.slice(1),
                    color: `var(--chart-${((index + 2) % 5) + 1})`,
                },
            }),
            {} as Record<string, { label: string; color: string }>,
        ),
    } satisfies ChartConfig

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 mb-6">
                {[1, 2, 3, 4].map((i) => (
                    <div
                        key={i}
                        className="bg-card rounded-lg p-4 animate-pulse">
                        <div className="h-4 bg-muted rounded mb-4"></div>
                        <div className="h-32 bg-muted rounded"></div>
                    </div>
                ))}
            </div>
        )
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 mb-6">
            {/* Incidents by Vessel - Horizontal Bar Chart */}
            <Card>
                <HorizontalBarChartComponent
                    chartConfig={vesselChartConfig}
                    chartData={vesselChartData}
                    cardTitle="Incidents by Vessel"
                    cardInfo={vesselChartInfo}
                />
            </Card>

            {/* Incidents by Type - Pie Chart */}
            <Card className="grid space-y-0">
                {incidentTypeCounts.length > 0 ? (
                    <>
                        <div>
                            <P>Incidents by Type</P>
                            <span className="text-curious-blue-400 text-sm">
                                Most: {incidentTypeCounts[0]?.type} (
                                {incidentTypeCounts[0]?.count})
                            </span>
                        </div>
                        <PieChartComponent
                            chartConfig={typeChartConfig}
                            chartData={typeChartData}
                        />
                    </>
                ) : (
                    <div className="h-32 flex items-center justify-center text-muted-foreground">
                        No incident type data
                    </div>
                )}
            </Card>

            {/* Incidents by Contributing Factor - Pie Chart */}
            <Card className="grid space-y-0">
                {contributingFactorCounts.length > 0 ? (
                    <>
                        <div>
                            <P>Contributing Factors</P>
                            <span className="text-curious-blue-400 text-sm">
                                Top: {contributingFactorCounts[0]?.factor} (
                                {contributingFactorCounts[0]?.count})
                            </span>
                        </div>
                        <PieChartComponent
                            chartConfig={factorChartConfig}
                            chartData={factorChartData}
                        />
                    </>
                ) : (
                    <div className="h-32 flex items-center justify-center text-muted-foreground">
                        No contributing factor data
                    </div>
                )}
            </Card>

            {/* Incidents by Treatment - Pie Chart */}
            <Card className="grid space-y-0">
                {treatmentCounts.length > 0 ? (
                    <>
                        <div>
                            <P>Treatment Types</P>
                            <span className="text-curious-blue-400 text-sm">
                                Most: {treatmentCounts[0]?.treatment} (
                                {treatmentCounts[0]?.count})
                            </span>
                        </div>
                        <PieChartComponent
                            chartConfig={treatmentChartConfig}
                            chartData={treatmentChartData}
                        />
                    </>
                ) : (
                    <div className="h-32 flex items-center justify-center text-muted-foreground">
                        No treatment data
                    </div>
                )}
            </Card>
        </div>
    )
}
