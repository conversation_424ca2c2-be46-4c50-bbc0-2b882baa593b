import gql from 'graphql-tag'

export const DownloadEventType_PassengerDropFacilities = gql`
    query DownloadEventType_PassengerDropFacilities(
        $limit: Int = 100
        $offset: Int = 0
        $filter: EventType_PassengerDropFacilityFilterFields = {}
    ) {
        readEventType_PassengerDropFacilities(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                time
                title
                fuelLevel
                paxOn
                paxOff
                lat
                long
                type
                tripEventID
                geoLocationID
            }
        }
    }
`
