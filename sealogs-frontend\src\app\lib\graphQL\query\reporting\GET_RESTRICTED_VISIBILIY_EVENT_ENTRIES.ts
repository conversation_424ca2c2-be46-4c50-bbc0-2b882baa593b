import gql from "graphql-tag";

export const GET_RESTRICTED_VISIBILIY_EVENT_ENTRIES = gql`
query Restricted_Visbility_Event_Report($filter: LogBookEntryFilterFields = {}){
  readLogBookEntries(
    sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000
  ){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: RestrictedVisibility
            }
          }){
            nodes{
              id
              eventCategory
              created
              start
              end
              lastEdited
              eventType_RestrictedVisibility{
                id
                created
                crossingTime
                startLocation{
                  id
                  title
                  lat
                  long
                }
                crossedTime
                endLocation{
                  id
                  title
                  lat 
                  long
                }
                estSafeSpeed
                approxSafeSpeed
                crewBriefing
                navLights
                soundSignals
                lookout
                soundSignal
                soundSignals
                radarWatch
                radioWatch
                report
              }
            }
          }
        }
      }
    }
  }
}
`