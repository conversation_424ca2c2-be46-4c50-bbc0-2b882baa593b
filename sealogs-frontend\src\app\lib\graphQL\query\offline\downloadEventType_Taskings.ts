import gql from 'graphql-tag'

export const DownloadEventType_Taskings = gql`
    query DownloadEventType_Taskings(
        $limit: Int = 100
        $offset: Int = 0
        $filter: EventType_TaskingFilterFields = {}
    ) {
        readEventType_Taskings(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                time
                title
                lat
                long
                fuelLevel
                type
                operationType
                currentEntryID
                comments
                groupID
                status
                pausedTaskID
                openTaskID
                completedTaskID
                cgop
                sarop
                tripEventID
                geoLocationID
                vesselRescueID
                personRescueID
                towingChecklistID
                parentTaskingID
            }
        }
    }
`
