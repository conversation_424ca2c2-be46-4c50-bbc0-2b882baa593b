import gql from 'graphql-tag'

export const DownloadDangerousGoodsChecklists = gql`
    query DownloadDangerousGoodsChecklists(
        $limit: Int = 100
        $offset: Int = 0
        $filter: DangerousGoodsChecklistFilterFields = {}
    ) {
        readDangerousGoodsChecklists(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                vesselSecuredToWharf
                bravoFlagRaised
                twoCrewLoadingVessel
                fireHosesRiggedAndReady
                noSmokingSignagePosted
                spillKitAvailable
                fireExtinguishersAvailable
                dgDeclarationReceived
                loadPlanReceived
                msdsAvailable
                anyVehiclesSecureToVehicleDeck
                safetyAnnouncement
                vehicleStationaryAndSecure
                memberID
                vesselID
                tripReport_StopID
                riskFactors {
                    nodes {
                        id
                    }
                }
            }
        }
    }
`
