import gql from 'graphql-tag'

export const DownloadEngine_Usages = gql`
    query DownloadEngine_Usages(
        $limit: Int = 100
        $offset: Int = 0
        $filter: Engine_UsageFilterFields = {}
    ) {
        readEngine_Usages(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                lastScheduleHours
                isScheduled
                maintenanceScheduleID
                engineID
            }
        }
    }
`
