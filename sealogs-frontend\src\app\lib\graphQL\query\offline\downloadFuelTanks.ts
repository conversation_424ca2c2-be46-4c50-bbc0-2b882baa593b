import gql from 'graphql-tag'

export const DownloadFuelTanks = gql`
    query DownloadFuelTanks(
        $limit: Int = 100
        $offset: Int = 0
        $filter: FuelTankFilterFields = {}
    ) {
        readFuelTanks(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                title
                identifier
                archived
                componentCategory
                capacity
                safeFuelCapacity
                currentLevel
                lastEdited
                fuelType
                dipType
                dipConversions
                dipImportID
                dipImportRun
            }
        }
    }
`
