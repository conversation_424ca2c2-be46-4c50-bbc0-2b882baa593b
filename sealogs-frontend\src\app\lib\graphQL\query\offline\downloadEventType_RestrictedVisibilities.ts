import gql from 'graphql-tag'

export const DownloadEventType_RestrictedVisibilities = gql`
    query DownloadEventType_RestrictedVisibilities(
        $limit: Int = 100
        $offset: Int = 0
        $filter: EventType_RestrictedVisibilityFilterFields = {}
    ) {
        readEventType_RestrictedVisibilities(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                crossingTime
                estSafeSpeed
                stopAssessPlan
                crewBriefing
                navLights
                soundSignals
                lookout
                soundSignal
                radarWatch
                radioWatch
                crossedTime
                approxSafeSpeed
                report
                startLat
                startLong
                endLat
                endLong
                tripEventID
                startLocationID
                endLocationID
            }
        }
    }
`
