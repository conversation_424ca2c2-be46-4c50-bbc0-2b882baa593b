import gql from 'graphql-tag'

export const DownloadCrewDuties = gql`
    query DownloadCrewDuties(
        $limit: Int = 100
        $offset: Int = 0
        $filter: CrewDutyFilterFields = {}
    ) {
        readCrewDuties(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                title
                abbreviation
                archived
                clientID
                className
                lastEdited
            }
        }
    }
`
